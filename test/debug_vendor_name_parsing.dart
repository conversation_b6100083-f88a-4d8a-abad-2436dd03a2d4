import 'package:flutter_test/flutter_test.dart';
import '../lib/services/parser/mlkit_parser_service.dart';
import '../lib/utils/amount_utils.dart';
import 'mocks/mock_storage_service.dart';
import 'mocks/mock_entity_extractor.dart';

/// Debug verification tool for vendor name parsing bug fix
/// 
/// This standalone script allows manual verification of the vendor name parsing
/// improvements and provides performance benchmarking.
/// 
/// Usage:
/// - Run with `dart test/debug_vendor_name_parsing.dart`
/// - Uncomment specific test scenarios to focus on particular cases
/// - Use for manual verification and debugging
void main() async {
  // Initialize Flutter bindings for tests that need platform services
  TestWidgetsFlutterBinding.ensureInitialized();
  
  print('🔧 Vendor Name Parsing Debug Tool');
  print('==================================\n');

  final mockStorage = MockStorageService();
  await mockStorage.init();

  // Test scenarios from the bug report
  final userReportedScenarios = [
    {
      'text': 'com trua tai lux70 100k',
      'expectedAmount': 100000.0,
      'expectedCurrency': 'VND',
      'description': 'Original user reported issue - should parse 100k not 70'
    },
    {
      'text': 'com trua tai lux70 100',
      'expectedAmount': 100.0,
      'expectedCurrency': 'VND',
      'description': 'Variant without abbreviation - should parse 100 not 70'
    },
    {
      'text': 'com trua tai Lux70 100k',
      'expectedAmount': 100000.0,
      'expectedCurrency': 'VND',
      'description': 'Case variation - should parse 100k not 70'
    },
    {
      'text': 'dinner at lux70 100k vnd',
      'expectedAmount': 100000.0,
      'expectedCurrency': 'VND',
      'description': 'With explicit currency - should parse 100k VND not 70'
    },
  ];

  print('📋 Testing User Reported Scenarios');
  print('-----------------------------------');
  await _testScenarios(mockStorage, userReportedScenarios);

  // Additional edge cases
  final edgeCaseScenarios = [
    {
      'text': '70Lux 100k',
      'expectedAmount': 100000.0,
      'expectedCurrency': 'VND',
      'description': 'Number at start of vendor name'
    },
    {
      'text': 'Shop123 Mall456 100k',
      'expectedAmount': 100000.0,
      'expectedCurrency': 'VND',
      'description': 'Multiple embedded numbers'
    },
    {
      'text': 'Hotel80 100 fee',
      'expectedAmount': 100.0,
      'expectedCurrency': 'VND',
      'description': 'Close to 20x threshold (100/80 = 1.25x)'
    },
    {
      'text': 'Store5 100',
      'expectedAmount': 100.0,
      'expectedCurrency': 'VND',
      'description': 'Exactly 20x threshold (100/5 = 20x)'
    },
    {
      'text': 'Cafe999 1k',
      'expectedAmount': 1000.0,
      'expectedCurrency': 'VND',
      'description': 'Abbreviation preference over large embedded number'
    },
  ];

  print('\n🔍 Testing Edge Case Scenarios');
  print('-------------------------------');
  await _testScenarios(mockStorage, edgeCaseScenarios);

  // Performance benchmarking
  print('\n⚡ Performance Benchmarking');
  print('---------------------------');
  await _performanceBenchmark(mockStorage);

  // AmountUtils verification
  print('\n🧮 AmountUtils Verification');
  print('---------------------------');
  await _testAmountUtils();

  print('\n✅ Debug verification complete!');
  print('================================');
}

/// Test a list of scenarios and report results
Future<void> _testScenarios(MockStorageService mockStorage, List<Map<String, dynamic>> scenarios) async {
  for (int i = 0; i < scenarios.length; i++) {
    final scenario = scenarios[i];
    final text = scenario['text'] as String;
    final expectedAmount = scenario['expectedAmount'] as double;
    final expectedCurrency = scenario['expectedCurrency'] as String;
    final description = scenario['description'] as String;

    print('${i + 1}. Testing: "$text"');
    print('   Expected: ${expectedAmount.toStringAsFixed(0)} $expectedCurrency');
    print('   Description: $description');

    try {
      // Reset singleton for each test
      MlKitParserService.resetInstance();

      // Create mock extractor that simulates ML Kit finding embedded numbers
      final mockExtractor = _createMockForScenario(text);
      final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);

      final result = await service.parseTransaction(text);

      if (result.isSuccess || result.requiresUserInput) {
        final actualAmount = result.transaction.amount;
        final actualCurrency = result.transaction.currencyCode;
        
        final amountMatch = actualAmount == expectedAmount;
        final currencyMatch = actualCurrency == expectedCurrency;
        
        if (amountMatch && currencyMatch) {
          print('   ✅ PASS: ${actualAmount.toStringAsFixed(0)} $actualCurrency');
        } else {
          print('   ❌ FAIL: ${actualAmount.toStringAsFixed(0)} $actualCurrency');
          if (!amountMatch) print('      Amount mismatch: expected $expectedAmount, got $actualAmount');
          if (!currencyMatch) print('      Currency mismatch: expected $expectedCurrency, got $actualCurrency');
        }
      } else {
        print('   ❌ FAIL: Parsing failed - ${result.error ?? "Unknown error"}');
      }
    } catch (e) {
      print('   ❌ ERROR: $e');
    }
    
    print('');
  }
}

/// Create appropriate mock extractor for a given scenario
MockEntityExtractor _createMockForScenario(String text) {
  // Extract embedded numbers from common patterns
  if (text.contains('lux70') || text.contains('Lux70')) {
    return MockEntityExtractorFactory.createLux70Scenario();
  } else if (text.contains('Shop123') && text.contains('Mall456')) {
    return MockEntityExtractorFactory.createMultipleEmbeddedScenario();
  } else if (text.contains('Hotel80')) {
    return MockEntityExtractorFactory.createExactPositionScenario(
      entityText: '80',
      start: 5,
      end: 7,
    );
  } else if (text.contains('Store5')) {
    return MockEntityExtractorFactory.createExactPositionScenario(
      entityText: '5',
      start: 5,
      end: 6,
    );
  } else if (text.contains('Cafe999')) {
    return MockEntityExtractorFactory.createExactPositionScenario(
      entityText: '999',
      start: 4,
      end: 7,
    );
  } else if (text.contains('70Lux')) {
    return MockEntityExtractorFactory.createExactPositionScenario(
      entityText: '70',
      start: 0,
      end: 2,
    );
  }
  
  // Default: try to find any embedded number
  final numberMatch = RegExp(r'\d+').firstMatch(text);
  if (numberMatch != null) {
    return MockEntityExtractorFactory.createExactPositionScenario(
      entityText: numberMatch.group(0)!,
      start: numberMatch.start,
      end: numberMatch.end,
    );
  }
  
  // No embedded numbers found
  return MockEntityExtractorFactory.createEmpty();
}

/// Benchmark parsing performance
Future<void> _performanceBenchmark(MockStorageService mockStorage) async {
  final testTexts = [
    'com trua tai lux70 100k',
    'Restaurant123 Hotel456 Shop789 Mall999 Cafe888 Store777 1.5M total',
    'Very long transaction description with multiple vendor names like Shop123 and Hotel456 and Restaurant789 but the real amount is 500k for this purchase',
  ];

  const iterations = 100;
  
  for (final text in testTexts) {
    print('Benchmarking: "${text.length > 50 ? text.substring(0, 50) + "..." : text}"');
    
    final stopwatch = Stopwatch()..start();
    
    for (int i = 0; i < iterations; i++) {
      MlKitParserService.resetInstance();
      final mockExtractor = _createMockForScenario(text);
      final service = await MlKitParserService.getInstance(mockStorage, entityExtractor: mockExtractor);
      await service.parseTransaction(text);
    }
    
    stopwatch.stop();
    
    final avgTime = stopwatch.elapsedMilliseconds / iterations;
    print('   Average time: ${avgTime.toStringAsFixed(2)}ms per parse');
    print('   Total time for $iterations iterations: ${stopwatch.elapsedMilliseconds}ms');
    print('');
  }
}

/// Test AmountUtils directly
Future<void> _testAmountUtils() async {
  final testCases = [
    {'text': '100k', 'expected': 100000.0},
    {'text': '2.5M', 'expected': 2500000.0},
    {'text': '1.2B', 'expected': 1200000000.0},
    {'text': '500', 'expected': 500.0},
    {'text': '\$100k', 'expected': 100000.0},
    {'text': '€2.5M', 'expected': 2500000.0},
    {'text': '100k vnd', 'expected': 100000.0},
  ];

  for (final testCase in testCases) {
    final text = testCase['text'] as String;
    final expected = testCase['expected'] as double;
    
    final result = AmountUtils.extractAmountFromText(text);
    
    if (result != null && result['amount'] == expected) {
      print('✅ "$text" -> ${result['amount']?.toStringAsFixed(0)} ${result['currency'] ?? ""}');
    } else {
      print('❌ "$text" -> ${result?['amount']?.toStringAsFixed(0) ?? "null"} (expected: ${expected.toStringAsFixed(0)})');
    }
  }
}
