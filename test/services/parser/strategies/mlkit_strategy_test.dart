import 'package:flutter_test/flutter_test.dart';
import 'package:uuid/uuid.dart';
import '../../../../lib/services/parser/strategies/mlkit_strategy.dart';
import '../../../../lib/services/parser/parsing_context.dart';
import '../../../../lib/models/transaction_model.dart';
import '../../../../lib/models/parse_result.dart';
import '../../../helpers/test_helpers.dart';
import '../../../mocks/mock_storage_service.dart';
import '../../../mocks/mock_entity_extractor.dart';
import '../../../mocks/mock_category_finder_service.dart';

void main() {
  group('MlKitStrategy Tests', () {
    late MlKitStrategy strategy;
    late MockStorageService mockStorage;
    late MockCategoryFinderService mockCategoryFinder;
    late MockEntityExtractor mockEntityExtractor;
    late Uuid uuid;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      mockStorage.setString('default_currency', 'USD');
      
      mockCategoryFinder = MockCategoryFinderService();
      mockEntityExtractor = MockEntityExtractor();
      uuid = const Uuid();
    });

    tearDown(() {
      mockEntityExtractor.reset();
      mockCategoryFinder.reset();
    });

    group('Strategy Interface Compliance', () {
      test('should return correct strategy name', () {
        strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true, // ML Kit available
        );
        
        expect(strategy.name, equals('MlKitStrategy'));
      });

      test('should return ParseResult when parsing succeeds', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Spent \$25.50 on coffee',
          entityText: '\$25.50',
          start: 6,
          end: 12,
        );
        
        strategy = MlKitStrategy(
          mockExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: 'Spent \$25.50 on coffee');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(25.50));
      });

      test('should return null when strategy fails', () async {
        // Create strategy with null extractor and ML Kit unavailable
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: ''); // Empty text should cause failure
        final result = await strategy.execute(context);

        // Strategy should handle gracefully and return a result, not null
        expect(result, isNotNull);
      });
    });

    group('ML Kit Available Cases', () {
      test('should process ML Kit money entities correctly', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Lunch at restaurant \$35.75',
          entityText: '\$35.75',
          start: 19,
          end: 25,
        );
        
        strategy = MlKitStrategy(
          mockExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: 'Lunch at restaurant \$35.75');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(35.75));
        expect(result!.transaction.currencyCode, equals('USD'));
      });

      test('should process ML Kit datetime entities', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMultipleEntities(
          moneyText: '€50.00',
          moneyStart: 0,
          moneyEnd: 6,
          dateTimeText: 'yesterday',
          dateTimeStart: 15,
          dateTimeEnd: 24,
        );
        
        strategy = MlKitStrategy(
          mockExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: '€50.00 dinner yesterday');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(50.00));
        expect(result!.transaction.currencyCode, equals('EUR'));
      });

      test('should handle ML Kit extraction errors gracefully', () async {
        mockEntityExtractor.setShouldThrowError(true, 'ML Kit extraction failed');
        
        strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: 'Coffee \$5.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should fall back to raw number finder
        expect(result!.transaction.amount, equals(5.50));
      });

      test('should handle abbreviations in ML Kit entities', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Investment 2.5k USD',
          entityText: '2.5k USD',
          start: 11,
          end: 19,
        );
        
        strategy = MlKitStrategy(
          mockExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: 'Investment 2.5k USD');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(2500.0));
        expect(result!.transaction.currencyCode, equals('USD'));
      });
    });

    group('ML Kit Unavailable Cases', () {
      test('should work with null EntityExtractor', () async {
        strategy = MlKitStrategy(
          null, // No entity extractor
          mockStorage,
          mockCategoryFinder,
          uuid,
          false, // ML Kit not available
        );

        final context = ParsingContext(text: 'Coffee \$4.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should use raw number finder only
        expect(result!.transaction.amount, equals(4.50));
      });

      test('should work when ML Kit flag is false', () async {
        strategy = MlKitStrategy(
          mockEntityExtractor, // Extractor present but disabled
          mockStorage,
          mockCategoryFinder,
          uuid,
          false, // ML Kit disabled
        );

        final context = ParsingContext(text: 'Taxi ride \$15.00');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(15.00));
      });

      test('should work when EntityExtractor is not initialized', () async {
        mockEntityExtractor.setInitialized(false);
        
        strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true, // ML Kit available but extractor not initialized
        );

        final context = ParsingContext(text: 'Grocery shopping \$85.25');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(85.25));
      });
    });

    group('Candidate Consolidation', () {
      test('should consolidate ML Kit and raw finder candidates', () async {
        // ML Kit finds one amount, raw finder finds additional amounts
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Paid \$50 tip \$10 total \$60',
          entityText: '\$50',
          start: 5,
          end: 8,
        );
        
        strategy = MlKitStrategy(
          mockExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: 'Paid \$50 tip \$10 total \$60');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should detect multiple amounts and trigger ambiguity
        expect(result!.status, equals(ParseStatus.ambiguousAmount));
      });

      test('should deduplicate identical candidates', () async {
        // Both ML Kit and raw finder find the same amount
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Coffee \$5.50',
          entityText: '\$5.50',
          start: 7,
          end: 12,
        );
        
        strategy = MlKitStrategy(
          mockExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: 'Coffee \$5.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should not be ambiguous since it's the same amount
        expect(result!.transaction.amount, equals(5.50));
        expect(result!.status, isNot(equals(ParseStatus.ambiguousAmount)));
      });

      test('should sort candidates by position', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: '\$100 spent on \$25 coffee and \$75 lunch',
          entityText: '\$100',
          start: 0,
          end: 4,
        );
        
        strategy = MlKitStrategy(
          mockExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: '\$100 spent on \$25 coffee and \$75 lunch');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should detect multiple amounts
        expect(result!.status, equals(ParseStatus.ambiguousAmount));
      });
    });

    group('Ambiguity Detection', () {
      test('should detect multiple unique amounts as ambiguous', () async {
        strategy = MlKitStrategy(
          null, // Use raw finder only for predictable results
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Bill \$50 tip \$10');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.status, equals(ParseStatus.ambiguousAmount));
        expect(result!.candidateAmounts, containsAll([50.0, 10.0]));
      });

      test('should not be ambiguous with single amount', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Coffee \$5.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.status, isNot(equals(ParseStatus.ambiguousAmount)));
        expect(result!.transaction.amount, equals(5.50));
      });

      test('should return missingAmount when no amounts found', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Had lunch today');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.status, equals(ParseStatus.missingAmount));
      });

      test('should handle duplicate amounts correctly', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Coffee \$5 and tea \$5');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Same amount twice should not be ambiguous
        expect(result!.status, isNot(equals(ParseStatus.ambiguousAmount)));
        expect(result!.transaction.amount, equals(5.0));
      });
    });

    group('Best Candidate Selection', () {
      test('should prefer non-embedded amounts', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        // "Lux68" scenario - 68 is embedded in vendor name, 2m is the real amount
        final context = ParsingContext(text: 'com trua tai Lux68 2m');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(2000000.0)); // 2m = 2 million
      });

      test('should prefer amounts with abbreviations', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Investment 100 or 2.5k');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should detect ambiguity but prefer abbreviated amount in selection logic
        expect(result!.status, equals(ParseStatus.ambiguousAmount));
        expect(result!.candidateAmounts, containsAll([100.0, 2500.0]));
      });

      test('should prefer amounts with currency information', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Payment 100 or \$50',
          entityText: '\$50',
          start: 15,
          end: 18,
        );

        strategy = MlKitStrategy(
          mockExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: 'Payment 100 or \$50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.status, equals(ParseStatus.ambiguousAmount));
      });
    });

    group('Currency Detection', () {
      test('should extract currency from ML Kit entities', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Dinner €45.50',
          entityText: '€45.50',
          start: 7,
          end: 13,
        );

        strategy = MlKitStrategy(
          mockExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
        );

        final context = ParsingContext(text: 'Dinner €45.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.currencyCode, equals('EUR'));
      });

      test('should extract currency from text symbols', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Shopping £75.25');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.currencyCode, equals('GBP'));
      });

      test('should extract currency from text codes', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Hotel 150 JPY');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.currencyCode, equals('JPY'));
      });

      test('should use default currency when none found', () async {
        mockStorage.setString('default_currency', 'CAD');

        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Coffee 5.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.currencyCode, equals('CAD'));
      });
    });

    group('Transaction Type Detection', () {
      test('should detect expense transactions', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Spent \$25 on lunch');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.type, equals(TransactionType.expense));
      });

      test('should detect income transactions', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Received salary \$3000');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.type, equals(TransactionType.income));
      });

      test('should detect loan transactions', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Borrowed \$500 from friend');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.type, equals(TransactionType.loan));
      });

      test('should handle negative amounts as expenses', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: '-\$50 withdrawal');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.type, equals(TransactionType.expense));
      });

      test('should return needsType for ambiguous transactions', () async {
        strategy = MlKitStrategy(
          null,
          mockStorage,
          mockCategoryFinder,
          uuid,
          false,
        );

        final context = ParsingContext(text: 'Transaction \$100');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.status, equals(ParseStatus.needsType));
      });
    });
  });
}
