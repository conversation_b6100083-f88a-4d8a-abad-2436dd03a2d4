import 'dart:async';
import '../../lib/services/parser/learned_association_service.dart';
import '../../lib/models/transaction_model.dart';

/// Mock implementation of LearnedAssociationService for testing purposes.
/// 
/// This mock follows the established patterns from MockStorageService and
/// MockLocalizationService, providing configurable responses, error simulation,
/// and async delay simulation.
class MockLearnedAssociationService {
  final Map<String, LearnedAssociation> _mockAssociations = {};
  bool _shouldThrowError = false;
  String? _errorMessage;
  Duration? _delay;

  /// Configure the mock to return a specific association for a text
  void setMockAssociation(String text, LearnedAssociation association) {
    _mockAssociations[_normalizeText(text)] = association;
  }

  /// Clear all mock associations
  void clearMockAssociations() {
    _mockAssociations.clear();
  }

  /// Configure the mock to throw errors
  void simulateError(bool shouldThrow, [String? errorMessage]) {
    _shouldThrowError = shouldThrow;
    _errorMessage = errorMessage;
  }

  /// Configure the mock to simulate async delays
  void setDelay(Duration? delay) {
    _delay = delay;
  }

  /// Reset the mock to initial state
  void reset() {
    _mockAssociations.clear();
    _shouldThrowError = false;
    _errorMessage = null;
    _delay = null;
  }

  /// Mock implementation of getAssociation method
  Future<LearnedAssociation?> getAssociation(String text) async {
    if (_delay != null) {
      await Future.delayed(_delay!);
    }

    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock LearnedAssociationService error');
    }

    final normalizedText = _normalizeText(text);
    
    // First try exact match
    if (_mockAssociations.containsKey(normalizedText)) {
      return _mockAssociations[normalizedText];
    }

    // Then try partial matches (similar to real service behavior)
    for (final entry in _mockAssociations.entries) {
      if (normalizedText.contains(entry.key) || entry.key.contains(normalizedText)) {
        return entry.value;
      }
    }

    return null;
  }

  /// Mock implementation of learn method
  Future<void> learn(String text, {TransactionType? type, String? categoryId, double? confirmedAmount}) async {
    if (_delay != null) {
      await Future.delayed(_delay!);
    }

    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock LearnedAssociationService error');
    }

    if (text.trim().isEmpty || (type == null && categoryId == null && confirmedAmount == null)) {
      return; // Nothing to learn
    }

    final normalizedText = _normalizeText(text);
    final existingAssociation = _mockAssociations[normalizedText];

    // Create or update association
    final updatedAssociation = LearnedAssociation(
      type: type ?? existingAssociation?.type,
      categoryId: categoryId ?? existingAssociation?.categoryId,
      confirmedAmount: confirmedAmount ?? existingAssociation?.confirmedAmount,
      lastUpdated: DateTime.now(),
      confidence: (existingAssociation?.confidence ?? 0) + 1,
    );

    _mockAssociations[normalizedText] = updatedAssociation;
  }

  /// Mock implementation of getAllAssociations method
  Future<Map<String, LearnedAssociation>> getAllAssociations() async {
    if (_delay != null) {
      await Future.delayed(_delay!);
    }

    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock LearnedAssociationService error');
    }

    return Map.unmodifiable(_mockAssociations);
  }

  /// Mock implementation of clearAllAssociations method
  Future<void> clearAllAssociations() async {
    if (_delay != null) {
      await Future.delayed(_delay!);
    }

    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock LearnedAssociationService error');
    }

    _mockAssociations.clear();
  }

  /// Mock implementation of exportAssociations method
  Future<String?> exportAssociations() async {
    if (_delay != null) {
      await Future.delayed(_delay!);
    }

    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock LearnedAssociationService error');
    }

    if (_mockAssociations.isEmpty) {
      return null;
    }

    // Simple JSON-like export for testing
    final entries = _mockAssociations.entries.map((e) => '"${e.key}": ${_associationToJsonString(e.value)}').join(', ');
    return '{$entries}';
  }

  /// Normalize text similar to the real service
  String _normalizeText(String text) {
    return text.toLowerCase().trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// Convert association to JSON string for export
  String _associationToJsonString(LearnedAssociation association) {
    final json = association.toJson();
    final entries = json.entries.map((e) => '"${e.key}": "${e.value}"').join(', ');
    return '{$entries}';
  }

  /// Helper method to check if mock has association for a specific text
  bool hasMockAssociationFor(String text) {
    return _mockAssociations.containsKey(_normalizeText(text));
  }

  /// Helper method to get all mock data for testing
  Map<String, LearnedAssociation> get mockData => Map.unmodifiable(_mockAssociations);

  /// Helper method to check error state for testing
  bool get isErrorSimulated => _shouldThrowError;

  /// Helper method to check delay configuration for testing
  Duration? get currentDelay => _delay;

  /// Helper method to create a test association
  static LearnedAssociation createTestAssociation({
    TransactionType? type,
    String? categoryId,
    double? confirmedAmount,
    DateTime? lastUpdated,
    int confidence = 1,
  }) {
    return LearnedAssociation(
      type: type,
      categoryId: categoryId,
      confirmedAmount: confirmedAmount,
      lastUpdated: lastUpdated ?? DateTime.now(),
      confidence: confidence,
    );
  }
}
